package net.smbuhs.bbs

import android.app.Application
import android.util.Log
import com.tencent.smtt.export.external.TbsCoreSettings
import com.tencent.smtt.sdk.QbSdk
import com.tencent.smtt.sdk.QbSdk.PreInitCallback

class FlarumApplication : Application() {
    
    companion object {
        private const val TAG = "FlarumApplication"
    }
    
    override fun onCreate() {
        super.onCreate()
        
        // 初始化 X5 内核
        initX5WebView()
    }
    
    private fun initX5WebView() {
        // 在调用 TBS 初始化、创建 WebView 之前进行如下配置
        val map = HashMap<String, Any>()
        map[TbsCoreSettings.TBS_SETTINGS_USE_SPEEDY_CLASSLOADER] = true
        map[TbsCoreSettings.TBS_SETTINGS_USE_DEXLOADER_SERVICE] = true
        QbSdk.initTbsSettings(map)
        
        // 设置 X5 初始化回调
        QbSdk.setDownloadWithoutWifi(true)
        
        // 初始化 X5 内核
        QbSdk.initX5Environment(applicationContext, object : PreInitCallback {
            override fun onViewInitFinished(isSuccess: Boolean) {
                Log.d(TAG, "X5 内核初始化完成: $isSuccess")
                if (isSuccess) {
                    Log.d(TAG, "X5 内核加载成功")
                } else {
                    Log.w(TAG, "X5 内核加载失败，将使用系统 WebView")
                }
            }
            
            override fun onCoreInitFinished() {
                Log.d(TAG, "X5 内核初始化结束")
            }
        })
    }
}
