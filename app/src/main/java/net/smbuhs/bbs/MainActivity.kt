package net.smbuhs.bbs

import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity

class MainActivity : ComponentActivity() {
    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        // 直接启动 Flarum WebView Activity
        val intent = Intent(this, FlarumWebViewActivity::class.java)
        startActivity(intent)

        // 关闭当前 Activity，避免用户按返回键回到空白页面
        finish()
    }
}

